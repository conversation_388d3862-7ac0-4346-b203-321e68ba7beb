{"name": "shein-order-backend", "version": "1.0.0", "type": "commonjs", "scripts": {"dev": "NODE_ENV=development ts-node src/server.ts", "build": "tsc", "start": "NODE_ENV=production node dist/server.js"}, "dependencies": {"@types/nodemailer": "^6.4.17", "@types/sqlite3": "^5.1.0", "axios": "^1.10.0", "cheerio": "^1.1.0", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.18.2", "nodemailer": "^7.0.4", "puppeteer": "^24.11.2", "sqlite": "^5.1.1", "sqlite3": "^5.1.7"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/node": "^20.19.4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}