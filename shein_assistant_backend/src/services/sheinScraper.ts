import axios from "axios";
import * as cheerio from "cheerio";
import puppeteer from "puppeteer";
import { Product } from "../types";

export interface ScrapingResult {
  success: boolean;
  products: Product[];
  totalUsd: number;
  error?: string;
}

export class SheinScraper {
  private static readonly USER_AGENT =
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";

  private static readonly TIMEOUT = 30000; // 30 seconds

  /**
   * Resolve Shein share links to actual cart/product URLs
   */
  private static async resolveSheinLink(shareLink: string): Promise<string> {
    try {
      console.log(`Resolving share link: ${shareLink}`);

      // Handle different types of Shein links
      if (shareLink.includes("api-shein.shein.com/h5/sharejump/appjump")) {
        // This is a share jump link, we need to follow the redirect
        try {
          const response = await axios.get(shareLink, {
            maxRedirects: 10,
            timeout: this.TIMEOUT,
            headers: {
              "User-Agent": this.USER_AGENT,
              Accept:
                "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
              "Accept-Language": "en-US,en;q=0.5",
              "Accept-Encoding": "gzip, deflate",
              Connection: "keep-alive",
              "Upgrade-Insecure-Requests": "1",
            },
            validateStatus: function (status) {
              return status >= 200 && status < 400; // Accept redirects
            },
          });

          // Check if we got redirected to a proper Shein URL
          const finalUrl =
            response.request.res?.responseUrl ||
            response.config.url ||
            shareLink;
          console.log(`Final resolved URL: ${finalUrl}`);

          if (finalUrl.includes("shein.com")) {
            return finalUrl;
          }

          // If still not a proper Shein URL, try to extract from response
          if (response.data && typeof response.data === "string") {
            // Look for redirect URLs in the HTML
            const redirectMatch = response.data.match(
              /window\.location\.href\s*=\s*["']([^"']+)["']/
            );
            if (redirectMatch && redirectMatch[1].includes("shein.com")) {
              console.log(`Found redirect in HTML: ${redirectMatch[1]}`);
              return redirectMatch[1];
            }

            // Look for meta refresh redirects
            const metaMatch = response.data.match(
              /<meta[^>]*http-equiv=["']refresh["'][^>]*content=["'][^;]*;\s*url=([^"']+)["']/i
            );
            if (metaMatch && metaMatch[1].includes("shein.com")) {
              console.log(`Found meta redirect: ${metaMatch[1]}`);
              return metaMatch[1];
            }
          }

          return finalUrl;
        } catch (redirectError: any) {
          console.error(
            `Error following redirects for ${shareLink}:`,
            redirectError.message
          );
          return shareLink;
        }
      }

      // If it's already a direct Shein URL, return as is
      if (shareLink.includes("shein.com")) {
        return shareLink;
      }

      // For other cases, try to resolve
      return shareLink;
    } catch (error: any) {
      console.error(`Error resolving link ${shareLink}:`, error.message);
      return shareLink; // Return original link if resolution fails
    }
  }

  /**
   * Extract product information from a Shein cart page
   */
  private static async scrapeCartPage(url: string): Promise<ScrapingResult> {
    let browser;
    try {
      console.log(`🔍 Scraping cart page with JavaScript execution: ${url}`);

      // Launch Puppeteer browser with more stable configuration
      browser = await puppeteer.launch({
        headless: true,
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-dev-shm-usage",
          "--disable-accelerated-2d-canvas",
          "--no-first-run",
          "--no-zygote",
          "--disable-gpu",
          "--disable-web-security",
          "--disable-features=VizDisplayCompositor",
        ],
        defaultViewport: { width: 1366, height: 768 },
      });

      const page = await browser.newPage();

      // Set user agent and configure page
      await page.setUserAgent(this.USER_AGENT);

      // Set extra headers to appear more like a real browser
      await page.setExtraHTTPHeaders({
        "Accept-Language": "fr-FR,fr;q=0.9,en;q=0.8",
        Accept:
          "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
      });

      // Handle page errors gracefully
      page.on("error", (error) => {
        console.log("⚠️ Page error:", error.message);
      });

      page.on("pageerror", (error) => {
        console.log("⚠️ Page script error:", error.message);
      });

      // Navigate to the page with retry logic
      console.log(`📄 Loading page: ${url}`);

      let navigationSuccess = false;
      let attempts = 0;
      const maxAttempts = 3;

      while (!navigationSuccess && attempts < maxAttempts) {
        attempts++;
        try {
          console.log(`🔄 Navigation attempt ${attempts}/${maxAttempts}`);

          await page.goto(url, {
            waitUntil: "domcontentloaded",
            timeout: 30000,
          });

          navigationSuccess = true;
          console.log("✅ Page loaded successfully");
        } catch (navError: any) {
          console.log(
            `❌ Navigation attempt ${attempts} failed:`,
            navError.message
          );

          if (attempts === maxAttempts) {
            throw new Error(
              `Failed to load page after ${maxAttempts} attempts: ${navError.message}`
            );
          }

          // Wait before retry
          await new Promise((resolve) => setTimeout(resolve, 2000));
        }
      }

      // Wait for JavaScript to execute and content to load
      console.log(`⏳ Waiting for content to load...`);
      await new Promise((resolve) => setTimeout(resolve, 8000));

      // Try to wait for cart content specifically
      try {
        await page.waitForSelector(
          ".csl-cart-list, .bsc-cart-item, .cart-item, .empty-cart, .no-items",
          { timeout: 15000 }
        );
        console.log("✅ Cart content detected");
      } catch (error) {
        console.log(
          "⚠️ No cart content detected within timeout, proceeding anyway"
        );
      }

      // Get the page content after JavaScript execution
      const content = await page.content();
      const $ = cheerio.load(content);
      const products: Product[] = [];
      let totalUsd = 0;

      // Log page title for debugging
      const pageTitle = $("title").text().trim();
      console.log(`📋 Page title: "${pageTitle}"`);

      // Check if page contains cart-related content
      const bodyText = $("body").text().toLowerCase();
      const hasCartKeywords = [
        "cart",
        "panier",
        "shopping",
        "checkout",
        "commande",
      ].some((keyword) => bodyText.includes(keyword));
      console.log(`🛒 Page contains cart keywords: ${hasCartKeywords}`);

      // Log some of the page content for debugging
      console.log(`📝 Page content preview: ${bodyText.substring(0, 500)}...`);

      // Try different selectors for cart items (prioritizing Shein-specific classes)
      const cartSelectors = [
        ".csl-cart-list .csl-cart-item", // Main Shein cart container and items
        ".bsc-cart-item", // Individual cart item class
        ".csl-cart-item", // Alternative cart item class
        ".bsc-cart-item-main", // Cart item main content
        ".cart-item",
        ".shopping-cart-item",
        ".cart-product",
        '[data-testid="cart-item"]',
        ".product-item",
        ".item",
        ".goods-item",
        ".product-card",
        '[class*="cart"]',
        '[class*="item"]',
        '[class*="product"]',
        '[class*="bsc-cart"]',
        '[class*="csl-cart"]',
      ];

      let foundItems = false;
      console.log(`🔎 Trying ${cartSelectors.length} different selectors...`);

      for (const selector of cartSelectors) {
        const items = $(selector);
        if (items.length > 0) {
          foundItems = true;
          console.log(
            `Found ${items.length} items using selector: ${selector}`
          );

          items.each((index, element) => {
            const $item = $(element);

            // Extract product name - prioritize the actual product title
            let productName = "";

            // First, try to get the real product title from the title attribute
            const titleSelectors = [
              'a[class*="bsc-cart-item-goods-title__content"]',
              ".bsc-cart-item-goods-title__content",
              "a[title]",
              '[title*="Robe"]', // Look for dress titles
              '[title*="T-shirt"]', // Look for t-shirt titles
              '[title*="Pantalon"]', // Look for pants titles
              '[title*="Chemise"]', // Look for shirt titles
            ];

            for (const selector of titleSelectors) {
              const titleElement = $item.find(selector);

              if (titleElement.length > 0) {
                const titleAttr = titleElement.attr("title")?.trim();
                if (titleAttr && titleAttr.length > 15) {
                  // Ensure it's a real product title
                  productName = titleAttr;
                  console.log(
                    `✅ Found product title: ${productName.substring(0, 50)}...`
                  );
                  break;
                }

                const titleText = titleElement.text().trim();
                if (titleText && titleText.length > 15) {
                  productName = titleText;
                  console.log(
                    `✅ Found product title from text: ${productName.substring(
                      0,
                      50
                    )}...`
                  );
                  break;
                }
              }
            }

            // If no title found, try looking for any element with a long title attribute
            if (!productName) {
              const anyTitleElement = $item.find("[title]");

              anyTitleElement.each((i, el) => {
                const title = $(el).attr("title")?.trim();
                if (title && title.length > 20 && !productName) {
                  // Skip if it looks like size/color info
                  if (
                    !title.match(
                      /^(XS|S|M|L|XL|XXL|Noir|Blanc|Rouge|Bleu|Vert|Jaune|Rose|Violet|Gris|Marron|Orange|Nude|Multicolore|Or|Argent|Tabac)/i
                    )
                  ) {
                    productName = title;
                    console.log(
                      `✅ Found product title from fallback: ${productName.substring(
                        0,
                        50
                      )}...`
                    );
                    return false; // Break the loop
                  }
                }
              });
            }

            // Fallback to other selectors if still no name
            if (!productName) {
              const nameSelectors = [
                ".bsc-cart-item-goods-title__slot-left",
                ".bsc-cart-item-goods-title",
                ".product-name",
                ".item-name",
                ".title",
                ".name",
                "h1",
                "h2",
                "h3",
                "h4",
                "h5",
                '[data-testid="product-name"]',
                '[class*="product-title"]',
              ];

              for (const nameSelector of nameSelectors) {
                const foundName = $item.find(nameSelector).text().trim();
                if (foundName && foundName.length > 10) {
                  // Ensure it's not just size/color
                  productName = foundName;
                  console.log(
                    `✅ Found name with "${nameSelector}": ${productName}`
                  );
                  break;
                }
              }
            }

            // Clean up the product name - remove size/color info if it's mixed in
            if (productName) {
              // Remove common size/color patterns that might be appended
              productName = productName
                .replace(
                  /\s*\/\s*(XS|S|M|L|XL|XXL|XXXL|Taille\s+Unique|One\s+Size).*$/i,
                  ""
                )
                .replace(
                  /\s*-\s*(Noir|Blanc|Rouge|Bleu|Vert|Jaune|Rose|Violet|Gris|Marron|Orange|Nude|Multicolore|Or|Argent|Tabac).*$/i,
                  ""
                )
                .replace(
                  /\s*,\s*(XS|S|M|L|XL|XXL|XXXL|Taille\s+Unique|One\s+Size).*$/i,
                  ""
                )
                .trim();

              console.log(`🧹 Cleaned product name: ${productName}`);
            }

            // Extract price (Shein-specific selectors first)
            const priceSelectors = [
              ".bsc-cart-item-goods-price__top", // Exact Shein price class
              ".bsc-cart-item-main .price", // Shein cart item price
              ".bsc-cart-item .price", // Alternative Shein price
              ".csl-cart-item .price", // Alternative Shein price
              ".price",
              ".product-price",
              ".item-price",
              ".cost",
              ".amount",
              ".money",
              ".value",
              '[data-testid="price"]',
              '[class*="price"]',
              '[class*="cost"]',
              '[class*="amount"]',
              '[class*="money"]',
            ];

            let priceText = "";
            for (const priceSelector of priceSelectors) {
              const foundPrice = $item.find(priceSelector).text().trim();
              if (foundPrice && foundPrice.match(/[\d.,]+/)) {
                priceText = foundPrice;
                console.log(
                  `💰 Found price with "${priceSelector}": ${priceText}`
                );
                break;
              }
            }

            // If no price found with selectors, try to extract from all text
            if (!priceText || !priceText.match(/[\d.,]+/)) {
              const allText = $item.text();
              const priceMatches = allText.match(
                /\$[\d.,]+|\€[\d.,]+|[\d.,]+\s*\$|[\d.,]+\s*\€|USD\s*[\d.,]+|EUR\s*[\d.,]+/g
              );
              if (priceMatches && priceMatches.length > 0) {
                priceText = priceMatches[0];
                console.log(`🔍 Extracted price from text: ${priceText}`);
              } else {
                console.log(
                  `❌ No price found in text: ${allText.substring(0, 100)}...`
                );
              }
            }

            // Extract quantity
            const quantitySelectors = [
              ".quantity input",
              ".qty input",
              '[data-testid="quantity"]',
              ".amount-input",
              "input[type='number']",
              ".bsc-cart-item-quantity",
              "[class*='quantity']",
            ];

            let quantity = 1;
            for (const qtySelector of quantitySelectors) {
              const qtyElement = $item.find(qtySelector);
              const qtyValue = qtyElement.val() || qtyElement.text().trim();
              if (qtyValue && qtyValue.toString().match(/\d+/)) {
                quantity = parseInt(qtyValue.toString()) || 1;
                console.log(
                  `🔢 Found quantity with "${qtySelector}": ${quantity}`
                );
                break;
              }
            }

            // Parse price (remove currency symbols and extract number)
            let price = 0;
            if (priceText) {
              const priceMatch = priceText.match(/[\d.,]+/);
              price = priceMatch
                ? parseFloat(priceMatch[0].replace(",", "."))
                : 0;
            }

            console.log(
              `📊 Final parsed data - Name: "${productName}" | Price: $${price} | Qty: ${quantity}`
            );

            if (productName && productName.length > 3) {
              products.push({
                name: productName,
                price: price.toString(), // Store as string number, not formatted
                quantity: quantity,
                rawPrice: priceText,
              });

              totalUsd += price * quantity;
              console.log(`✅ Added product. Total USD so far: $${totalUsd}`);
            } else {
              console.log(`❌ Skipped item - insufficient data`);
            }
          });
          break; // Stop after finding items with the first working selector
        }
      }

      if (!foundItems) {
        console.log(
          `❌ No cart items found with any selector. Trying alternative approaches...`
        );

        // Try to extract from JSON data in script tags
        const scriptTags = $("script");
        console.log(
          `🔍 Found ${scriptTags.length} script tags, checking for cart data...`
        );

        scriptTags.each((index, element) => {
          const scriptContent = $(element).html();
          if (
            scriptContent &&
            scriptContent.includes("cart") &&
            scriptContent.includes("product")
          ) {
            try {
              console.log(
                `📜 Script tag ${index + 1} contains cart/product keywords`
              );

              // Look for JSON data containing cart information
              const jsonMatch = scriptContent.match(
                /window\.__INITIAL_STATE__\s*=\s*({.*?});/
              );
              if (jsonMatch) {
                const data = JSON.parse(jsonMatch[1]);
                console.log("✅ Found potential cart data in script tag");
                console.log(`📊 Data keys: ${Object.keys(data).join(", ")}`);
              }

              // Look for other common patterns
              const patterns = [
                /cartData\s*[:=]\s*({.*?})/,
                /products\s*[:=]\s*(\[.*?\])/,
                /items\s*[:=]\s*(\[.*?\])/,
              ];

              for (const pattern of patterns) {
                const match = scriptContent.match(pattern);
                if (match) {
                  console.log(
                    `🎯 Found potential data with pattern: ${pattern.source}`
                  );
                }
              }
            } catch (e) {
              console.log(`❌ Error parsing script tag ${index + 1}: ${e}`);
            }
          }
        });

        // Check if this might be a login/auth page
        const loginKeywords = [
          "login",
          "sign in",
          "connexion",
          "authentification",
        ];
        const hasLoginKeywords = loginKeywords.some((keyword) =>
          bodyText.includes(keyword)
        );
        if (hasLoginKeywords) {
          console.log(`🔐 Page appears to require authentication`);
        }

        // Check if this might be an error page
        const errorKeywords = [
          "error",
          "not found",
          "404",
          "erreur",
          "introuvable",
        ];
        const hasErrorKeywords = errorKeywords.some((keyword) =>
          bodyText.includes(keyword)
        );
        if (hasErrorKeywords) {
          console.log(`❌ Page appears to be an error page`);
        }
      }

      const errorMessage = foundItems
        ? undefined
        : `No products found in cart. Page title: "${pageTitle}". Has cart keywords: ${hasCartKeywords}. Content preview: ${bodyText.substring(
            0,
            200
          )}...`;

      return {
        success: products.length > 0,
        products,
        totalUsd: Math.round(totalUsd * 100) / 100, // Round to 2 decimal places
        error: errorMessage,
      };
    } catch (error: any) {
      console.error(`❌ Error scraping cart page ${url}:`, error.message);

      // Provide more specific error messages
      let errorMessage = `Failed to scrape cart: ${error.message}`;

      if (error.message.includes("frame was detached")) {
        errorMessage =
          "La page s'est fermée de manière inattendue. Cela peut être dû à une redirection ou à un problème de réseau.";
      } else if (error.message.includes("Navigation timeout")) {
        errorMessage =
          "La page met trop de temps à charger. Vérifiez votre connexion internet.";
      } else if (error.message.includes("net::ERR_")) {
        errorMessage =
          "Erreur de réseau. Vérifiez votre connexion internet et réessayez.";
      }

      return {
        success: false,
        products: [],
        totalUsd: 0,
        error: errorMessage,
      };
    } finally {
      // Clean up browser
      if (browser) {
        try {
          await browser.close();
          console.log("🔒 Browser closed");
        } catch (closeError) {
          console.error("Error closing browser:", closeError);
        }
      }
    }
  }

  /**
   * Main method to scrape a Shein cart link
   */
  static async scrapeCart(cartLink: string): Promise<ScrapingResult> {
    try {
      // First resolve the link if it's a share link
      const resolvedUrl = await this.resolveSheinLink(cartLink);
      console.log(`Resolved URL: ${resolvedUrl}`);

      // Then scrape the cart page
      return await this.scrapeCartPage(resolvedUrl);
    } catch (error: any) {
      console.error(`Error in scrapeCart for ${cartLink}:`, error.message);
      return {
        success: false,
        products: [],
        totalUsd: 0,
        error: `Failed to process cart link: ${error.message}`,
      };
    }
  }

  /**
   * Scrape multiple cart links for a client
   */
  static async scrapeMultipleCarts(
    cartLinks: string[]
  ): Promise<ScrapingResult> {
    const allProducts: Product[] = [];
    let totalUsd = 0;
    const errors: string[] = [];

    for (const link of cartLinks) {
      if (!link.trim()) continue; // Skip empty links

      const result = await this.scrapeCart(link);

      if (result.success) {
        allProducts.push(...result.products);
        totalUsd += result.totalUsd;
      } else if (result.error) {
        errors.push(`${link}: ${result.error}`);
      }

      // Add a small delay between requests to be respectful
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }

    return {
      success: allProducts.length > 0,
      products: allProducts,
      totalUsd: Math.round(totalUsd * 100) / 100,
      error: errors.length > 0 ? errors.join("; ") : undefined,
    };
  }
}
